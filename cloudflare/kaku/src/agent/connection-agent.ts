import {
  Agent,
  AgentContext,
  type Connection,
  type ConnectionContext,
  type WSMessage,
} from 'agents';
import { html } from 'hono/html';
import { refreshTensorFlowBoundingBox, stopCaptchaMonitoring } from '../browser';
import { CDP } from '../browser/simple-cdp';
import { CDPAttachedToTargetParams, CDPEvent } from '../browser/types/cdp-events';
import { ErrorContext, ErrorDisplay, ErrorService } from '../common/error';
import { encryptData, getEncryptionKey } from '../common/utils';
import { FormVisionResult } from '../form-generation/htmx-generator';
import { AnthropicLLMRepository } from '../llm/AnthropicLLMRepository';
import { LLMService } from '../llm/LLMService';
import { OpenAILLMRepository } from '../llm/OpenAILLMRepository';
import { COORDINATOR_CONSTANTS } from '../shared/coordinator-types';
import { templateEngine } from '../ui';
import { PlatformTypes } from '../ui/constants';
import { BrowserServiceFactory, RemoteBrowserService } from '../workflow/services';
import {
  FormSubmissionPayloadSource,
  FormSubmissionEvent as WorkflowFormSubmissionEvent,
} from '../workflow/types/ConnectionsWorkflowParams';
import { getPrimaryActionCoordinates } from '../workflow/utils/helpers';
import { CDPManager, FormHandler } from './modules';
import {
  CoordinateResolutionService,
  ElementCoordinateMapping,
  ElementCoordinateRequest,
} from './services/coordinate-resolution';
import { AgentState } from './types';
import { BoundingRect, CropBoxUpdateData, PageStatus } from './types/agent-state';
import { CaptchaSolvedPayload } from './types/captcha-solved';
import {
  Action,
  CaptchaBoundingBox,
  PageStateResult,
  PageStateResultWithOptionalCoordinates,
} from './types/extract-result';
import { FormSubmissionPayload } from './types/form-types';
import {
  BaseWebSocketEvent,
  FormSubmissionEvent as WebSocketFormSubmissionEvent,
} from './types/websocket-events';

export class Connections extends Agent<Env, AgentState> {
  initialState: AgentState = {
    userId: 'unknown',
    platformId: 'kazeel',
    referenceId: '',
    status: PageStatus.INITIAL,
    captchaSetupComplete: false,
    initializationStatus: 'initial',
    termsAndConditionsApproved: false,
    notifyUIChange: false,
  };
  cdpClient: CDP | null = null;
  targetSessionId: string | null = null;

  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );
  llmService: LLMService;
  coordinateResolutionService: CoordinateResolutionService;

  // Module instances
  formHandler!: FormHandler;
  cdpManager!: CDPManager;

  // Logging configuration
  private config = {
    debug: true, // Set to true for verbose logging
  };

  constructor(ctx: AgentContext, env: Env) {
    super(ctx, env);
    const openAIRepository = new OpenAILLMRepository(env.OPENAI_API_KEY, env.AI_GATEWAY_OPENAI_URL);
    const anthropicRepository = new AnthropicLLMRepository(
      env.ANTHROPIC_API_KEY,
      env.AI_GATEWAY_ANTHROPIC_URL,
    );
    this.llmService = new LLMService({
      primaryRepo: openAIRepository,
      secondaryRepo: anthropicRepository,
    });
    this.coordinateResolutionService = new CoordinateResolutionService(this.llmService);
    this.formHandler = new FormHandler();
    this.cdpManager = new CDPManager();
  }

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][connections-agent]', ...args);
    }
  }

  private warn(...args: any[]): void {
    console.error('[kazeel][connections-agent]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[kazeel][connections-workflow]', ...args);
  }

  /**
   * Initial setup of Durable object
   * Probably called only once when agentDO is created
   * Initializes crucial data required for the agent to process
   */
  public setup(platformId: PlatformTypes, userId: string, referenceId: string): void {
    this.setName(referenceId);
    this.setState({
      ...this.state,
      platformId: platformId,
      userId: userId,
      referenceId: referenceId,
    });
    // cleanup scheduler that runs after an hour late than the Coordinator DO
    // that acts as a fallback if the Coordinator DO fails to do so.
    this.scheduleSelfCleanup();
  }

  private async scheduleSelfCleanup() {
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY + 60 * 60 * 1000;
    this.schedule(new Date(deletionTime), 'deleteAll');
  }

  public async eagerlyInitializeResources(
    platform: PlatformTypes,
    userId: string,
    linkId: string,
  ): Promise<void> {
    try {
      if (!platform || !userId) {
        throw Error(
          `Invalid link. platform or userId is missing - platform: ${platform}, userId: ${userId}`,
        );
      }

      if (this.state.initializationStatus === 'completed') {
        return;
      }

      this.setState({
        ...this.state,
        initializationStatus: 'in_progress',
      });
      // Create browser session
      const browserSession = await this.browserService.createSession();

      // Set up CDP client
      const cdpClient = await this.setupCDPClient(browserSession.wsEndpoint);

      await this.cdpManager.setAuthAttach(cdpClient);

      cdpClient.Target.addEventListener('attachedToTarget', this.onAttachedToTarget.bind(this));

      //Create workflow
      const instance = await this.env.CONNECTIONS_WORKFLOW.create({
        params: {
          platformId: platform,
          userId,
          sessionId: browserSession.sessionId,
          linkId: linkId,
        },
      });

      // Update state with session and workflow info, and mark initialization as complete
      this.setState({
        ...this.state,
        sessionId: browserSession.sessionId,
        workflowId: instance.id,
        initializationStatus: 'completed',
        errorMessage: undefined,
      });
    } catch (error) {
      this.setState({
        ...this.state,
        initializationStatus: 'failed',
      });

      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: 'unknown',
          userId: userId,
          platformId: platform,
          referenceId: linkId,
        },
        this.env,
        'error',
      );
    }
  }

  private async setupCDPClient(webSocketDebuggerUrl: string) {
    this.cdpClient = new CDP({ webSocketDebuggerUrl });
    this.cdpManager.setupCDPErrorMonitoring(
      this.cdpClient,
      {
        userId: this.state.userId,
        platformId: this.state.platformId,
        referenceId: this.state.referenceId,
        sessionId: this.state.sessionId || 'unknown',
      },
      (error: ErrorContext) => this.handleAndDisplayError(error),
    );
    return this.cdpClient;
  }

  /**
   * Resolve element coordinates and return the mapping without state updates
   */
  private async resolveElementCoordinates(
    screenshot: string,
    formVisionResult: FormVisionResult,
  ): Promise<ElementCoordinateMapping> {
    const viewport = await this.cdpClient!.Page.getLayoutMetrics(undefined, this.targetSessionId!);
    const viewportWidth = viewport.cssLayoutViewport.clientWidth;
    const viewportHeight = viewport.cssLayoutViewport.clientHeight;

    const elementRequest: ElementCoordinateRequest = {
      fields: formVisionResult.controls.fields,
      buttons: formVisionResult.controls.buttons,
    };

    return await this.coordinateResolutionService.resolveElementCoordinates(
      screenshot,
      elementRequest,
      this.state.platformId,
      viewportWidth,
      viewportHeight,
    );
  }

  async onFormStateChange(screenshot: string, result: PageStateResult) {
    const isAuthenticated = result.classificationResult.screenInfo.authState === 'authenticated';
    const newStatus: AgentState['status'] = isAuthenticated
      ? PageStatus.COMPLETED
      : PageStatus.WAITING_FOR_HUMAN;

    const pageStateResult: PageStateResultWithOptionalCoordinates = {
      ...result,
      coordinatesResolved: false,
    };

    this.setState({
      ...this.state,
      status: newStatus,
      pageStateResult,
      history: this.state?.history ? [...this.state.history, result] : [result],
      notifyUIChange: true,
    });

    if (!isAuthenticated) {
      // Start coordinate resolution asynchronously
      const coordinatePromise = this.resolveCoordinatesAsync(screenshot, result.extractionResult);

      this.setState({
        ...this.state,
        coordinateResolutionPromise: coordinatePromise,
        notifyUIChange: false,
      });
    }
  }

  /**
   * Start coordinate resolution asynchronously in the background
   */
  private async resolveCoordinatesAsync(
    screenshot: string,
    formVisionResult: FormVisionResult,
  ): Promise<void> {
    try {
      const elementCoordinateMapping = await this.resolveElementCoordinates(
        screenshot,
        formVisionResult,
      );
      // Update state with resolved coordinates
      if (this.state.pageStateResult) {
        const updatedPageState: PageStateResultWithOptionalCoordinates = {
          ...this.state.pageStateResult,
          coordinatesResolved: true,
        };

        this.setState({
          ...this.state,
          pageStateResult: updatedPageState,
          elementCoordinateMapping,
          coordinateResolutionPromise: undefined, // Clear the promise
          notifyUIChange: false, // Don't trigger UI update for coordinate resolution
        });
      }
    } catch (error) {
      console.error('Background coordinate resolution failed:', error);

      // Clear the promise even on error
      this.setState({
        ...this.state,
        coordinateResolutionPromise: undefined,
        notifyUIChange: false,
      });
    }
  }

  async markWaitingForAgent() {
    this.setState({
      ...this.state,
      status: PageStatus.WAITING_FOR_AGENT,
      notifyUIChange: false,
    });
  }

  onStateUpdate(state: AgentState | undefined, _source: Connection | 'server'): void {
    if (!state) return;

    if (state.notifyUIChange) {
      const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
        ${templateEngine.generateContent(state, this.state.platformId)}
      </div>`;

      this.broadcast(uiUpdate as string);
    }
  }

  async onConnect(connection: Connection, _ctx: ConnectionContext): Promise<void> {
    this.log(`Connected: to ${this.name}. Initial status is ${this.state.status}`, connection.id);
    // State of interactivity for the end user. Can be paused or enabled.
    if (
      this.state.status === 'waiting-for-human' &&
      this.state.interactivity?.status === 'enabled'
    ) {
      this.broadcast(
        JSON.stringify({
          type: 'interactivity-status',
          status: this.state.interactivity.status,
          cropBox: this.state.interactivity.cropBox,
          inputBoxRects: this.state.interactivity.inputBoxRects,
          primaryActionCoordinates: this.state.interactivity.primaryActionCoordinates,
        }),
      );
    }
    if (this.state.status === PageStatus.INITIAL) {
      //Begin the flow
      await this.handleFlowInitiate({
        platform: this.state.platformId,
      });
    }

    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(this.state, this.state.platformId)}
    </div>`;
    this.broadcast(uiUpdate as string);
  }

  onClose(connection: Connection): void | Promise<void> {
    this.log(`Disconnected from ${this.name}`, connection.id);

    this.cdpManager.cleanupCDPErrorMonitoring();
  }

  override async onMessage(connection: Connection, message: WSMessage) {
    if (typeof message !== 'string') {
      console.error('Invalid message type received:', typeof message);
      return;
    }

    try {
      const event = JSON.parse(message) as BaseWebSocketEvent;
      this.log(`Received WebSocket event: ${event.type}`);
      await this.handleWebSocketEvent(event, connection);
    } catch (error) {
      console.error('Error processing WebSocket message:', error);
      throw new Error(
        `Failed to process WebSocket message: ${message}, Error: ${JSON.stringify(error)}`,
      );
    }
  }

  /**
   * Handle WebSocket events with type safety and consistent routing
   */
  private async handleWebSocketEvent(
    event: BaseWebSocketEvent,
    connection: Connection,
  ): Promise<void> {
    switch (event.type) {
      case 'agree_and_continue':
        await this.handleFlowInitiate({
          platform: this.state.platformId,
        });
        break;

      case 'decline_terms':
        await this.handleTermsDecline();
        break;

      case 'form_submission': {
        const formEvent = event as WebSocketFormSubmissionEvent;
        await this.handleInputSubmitted(formEvent);
        break;
      }

      case 'cropbox-update': {
        const cropEvent = event as CropBoxUpdateData;
        await this.handleCropBoxUpdate(cropEvent);
        break;
      }

      case 'retry':
        await this.handleRetry();
        break;

      case 'captcha_verification': {
        await this.handleCaptchaSolvedEvent({
          differencePercentage: 0,
          timestamp: event.timestamp,
          executionContextId: 0,
          source: 'manual_verification',
        });
        break;
      }

      default:
        this.log('Received unknown event type:', event.type);
        this.broadcast(JSON.stringify({ ...event }), [connection.id]);
        break;
    }
  }

  async deleteAll() {
    await this.ctx.storage.deleteAll();
    await this.ctx.storage.deleteAlarm();
  }

  async onAttachedToTarget(cdpParams: CDPEvent<CDPAttachedToTargetParams>) {
    await this.cdpManager.onAttachedToTarget(this.cdpClient!, cdpParams, (sessionId: string) => {
      this.targetSessionId = sessionId;
    });
  }

  async saveTargetTabInfo(targetId: string) {
    this.setState({
      ...this.state,
      targetId,
      notifyUIChange: false,
    });
  }
  async markTermsAccepted() {
    this.setState({
      ...this.state,
      termsAndConditionsApproved: true,
      notifyUIChange: true,
    });
  }

  async waitForInitialization(): Promise<void> {
    return new Promise((resolve, reject) => {
      const poll = () => {
        const status = this.state.initializationStatus;
        const errorMessage = this.state.errorMessage;

        if (status === 'completed') {
          resolve();
        } else if (status === 'failed') {
          console.error('Background initialization failed:', errorMessage);
          reject(new Error(`Initialization failed: ${errorMessage || 'Unknown error'}`));
        } else {
          setTimeout(poll, 100);
        }
      };

      poll();
    });
  }

  async handleFlowInitiate(config: { platform: PlatformTypes }) {
    if (
      this.state.initializationStatus !== PageStatus.COMPLETED &&
      !this.state.termsAndConditionsApproved
    ) {
      return;
    }

    const handleInitializationError = async (error: unknown): Promise<void> => {
      const { userId, referenceId } = this.state;

      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: 'flow_initiate_failed',
          userId,
          platformId: config.platform,
          referenceId,
        },
        this.env,
        'error',
      );
    };

    this.markTermsAccepted();

    try {
      await this.waitForInitialization();
      this.log('Flow initiation completed successfully');
    } catch (error) {
      await handleInitializationError(error);
    }
  }

  /**
   * Handle when user declines terms and conditions
   */
  async handleTermsDecline(): Promise<void> {
    this.log('User declined terms and conditions');
    this.setState({
      ...this.state,
      termsAndConditionsApproved: false,
      status: PageStatus.INITIAL,
      notifyUIChange: true,
    });
  }

  async handleAndDisplayError(errorContext: ErrorContext): Promise<void> {
    const processedError = ErrorService.processErrorForUI(errorContext);

    this.setState({
      ...this.state,
      status: PageStatus.ERROR,
      errorMessage: processedError.userMessage,
      notifyUIChange: true,
    });

    const errorUI = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${ErrorDisplay(processedError)}
    </div>`;

    this.broadcast(errorUI as string);
  }

  /**
   * Streamlined form submission handler that builds actions directly from form data
   * Eliminates complex filtering and mapping logic
   */
  private async handleInputSubmitted(payload: FormSubmissionPayload): Promise<void> {
    this.setState({ ...this.state, status: PageStatus.WAITING_FOR_AGENT, notifyUIChange: true });
    if (this.state.coordinateResolutionPromise) {
      await this.state.coordinateResolutionPromise;
    }
    const actions = await this.formHandler.validatePayloadAndPrepareActions(
      payload,
      this.state.elementCoordinateMapping,
      this.state.pageStateResult,
      {
        onFormSubmissionError: (error: unknown) => {
          this.log('[FORM] Form submission error:', error);
          this.handleFormSubmissionError(error);
        },
      },
    );
    if (actions) {
      await this.sendActionsToWorkflow(
        actions,
        this.state.workflowId!,
        this.state.elementCoordinateMapping,
      );
    }
  }

  /**
   * Helper method to send actions to workflow
   */
  private async sendActionsToWorkflow(
    actions: Action[],
    workflowId: string,
    elementCoordinateMapping: ElementCoordinateMapping | undefined,
  ): Promise<void> {
    const encryptionKey = await getEncryptionKey();
    const encrypted = await encryptData(JSON.stringify({ actions }), encryptionKey);

    const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
    const payloadWithCoordinates: WorkflowFormSubmissionEvent = {
      payload: encrypted,
      coordinates: elementCoordinateMapping,
      source: FormSubmissionPayloadSource.PAGE_FORM_SUBMISSION,
    };

    await workflow.sendEvent({
      type: 'form-submission',
      payload: payloadWithCoordinates,
    });
  }

  /**
   * Handle form submission errors (callback from FormHandler)
   */
  private async handleFormSubmissionError(error: unknown): Promise<void> {
    console.error('Failed to handle form submission:', error);
    await ErrorService.handleGenericError(
      'agent',
      'SYSTEM_ERROR',
      error,
      {
        sessionId: this.targetSessionId ?? 'not_initialized',
        userId: this.state.userId,
        platformId: this.state.platformId,
        referenceId: this.state.referenceId,
      },
      this.env,
      'error',
    );
  }

  async sendTwoFactorAuthenticationCompletedEvent() {
    const workflowId = this.state.workflowId;
    if (workflowId) {
      const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
      this.setState({
        ...this.state,
        status: PageStatus.WAITING_FOR_AGENT,
        notifyUIChange: true,
      });
      await workflow.sendEvent({
        type: 'form-submission',
        payload: {
          payload: '',
          source: FormSubmissionPayloadSource.TWO_FACTOR_AUTHENTICATION_COMPLETION,
        },
      });
    } else {
      new Error('workflowID should not be null');
    }
  }

  async handleCaptchaSolvedEvent(differenceData: CaptchaSolvedPayload) {
    try {
      this.log(
        `Captcha solved event received, source: ${differenceData.source} - ${differenceData.differencePercentage.toFixed(2)}%`,
      );
      if (!this.state.workflowId) {
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('workflowID should not be null'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: this.state.userId,
            platformId: this.state.platformId,
            referenceId: this.state.referenceId,
          },
          this.env,
          'error',
        );
        return;
      }

      // Send captcha solved notification to workflow
      const workflowId = this.state.workflowId;
      const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
      await workflow.sendEvent({
        type: 'captcha-solved',
        payload: differenceData,
      });
      this.broadcast(JSON.stringify({ type: 'interactivity-status', status: 'completed' }));
    } catch (error) {
      console.error('Failed to handle captcha solved event:', error);

      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: this.targetSessionId ?? 'not_initialized',
          userId: this.state.userId,
          platformId: this.state.platformId,
          referenceId: this.state.referenceId,
        },
        this.env,
        'error',
      );
    }
  }

  async stopInteractivity() {
    if (this.cdpClient) {
      await stopCaptchaMonitoring(this.cdpClient, this.targetSessionId!);
    }
  }

  async handleCropBoxUpdate(data: CropBoxUpdateData) {
    if (this.state.interactivity?.status === 'completed') {
      return;
    }
    try {
      const newCropBox = data.cropBox;
      const inputBoxRects = data.inputBoxRects;

      if (this.isCaptchaBoundingBox(newCropBox) && this.isBoundingRectArray(inputBoxRects)) {
        const primaryActionCoordinates = getPrimaryActionCoordinates(
          this.state.pageStateResult,
          this.state.elementCoordinateMapping,
        );

        this.setState({
          ...this.state,
          notifyUIChange: true,
          interactivity: {
            status: 'enabled',
            cropBox: newCropBox,
            inputBoxRects,
            primaryActionCoordinates,
          },
        });
        this.broadcast(
          JSON.stringify({
            type: 'interactivity-status',
            status: 'enabled',
            cropBox: newCropBox,
            inputBoxRects,
            primaryActionCoordinates,
          }),
        );
      } else {
        throw Error(`Invalid cropbox input type: ${JSON.stringify(data)}`);
      }
    } catch (error) {
      console.error('Failed to handle CropBoxUpdate event:', error);

      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: this.targetSessionId ?? 'not_initialized',
          userId: this.state.userId,
          platformId: this.state.platformId,
          referenceId: this.state.referenceId,
        },
        this.env,
        'error',
      );
    }
  }

  async handleCaptchaDetected(
    executionContextId: number,
    viewport: { width: number; height: number },
  ) {
    if (!this.cdpClient) {
      console.error('Cannot handle captcha detected: cdpClient not initialized');
      return;
    }

    if (this.state.status === 'error') {
      this.log('Agent is in error state, skipping captcha detection setup');
      throw new Error('Agent is in error state - cannot proceed with captcha detection');
    }

    if (this.state.captchaSetupComplete) {
      this.log(
        'Captcha setup already complete, skipping re-initialization. Refreshing bounding box',
      );
      await refreshTensorFlowBoundingBox(
        this.cdpClient,
        executionContextId,
        viewport,
        this.targetSessionId!,
      );
      return;
    }

    await this.cdpManager.startCaptchaMonitoring(
      this.cdpClient,
      this.targetSessionId!,
      executionContextId,
      (event: CaptchaSolvedPayload) => this.handleCaptchaSolvedEvent(event),
    );

    this.setState({
      ...this.state,
      status: PageStatus.WAITING_FOR_HUMAN,
      notifyUIChange: true,
      interactivity: {
        ...this.state.interactivity!,
        status: 'enabled',
      },
      captchaSetupComplete: true,
    });
  }

  private isCaptchaBoundingBox(obj: unknown): obj is CaptchaBoundingBox {
    return (
      typeof obj === 'object' &&
      obj !== null &&
      typeof (obj as Record<string, unknown>).x === 'number' &&
      typeof (obj as Record<string, unknown>).y === 'number' &&
      typeof (obj as Record<string, unknown>).width === 'number' &&
      typeof (obj as Record<string, unknown>).height === 'number'
    );
  }

  private isBoundingRectArray(obj: unknown): obj is BoundingRect[] {
    return (
      Array.isArray(obj) &&
      obj.every((entry) => {
        return (
          typeof entry.id === 'string' &&
          typeof entry.x === 'number' &&
          typeof entry.y === 'number' &&
          typeof entry.width === 'number' &&
          typeof entry.height === 'number'
        );
      })
    );
  }

  private async cleanupResources(): Promise<void> {
    if (!this.state.sessionId) {
      this.log('No browser session to close');
      return;
    }
    await this.browserService.closeSession(this.state.sessionId);
  }

  async handleRetry(): Promise<void> {
    //TODO: Retry will generate a new link
  }
}
