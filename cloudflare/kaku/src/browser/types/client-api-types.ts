/**
 * TypeScript interfaces for client-side browser scripts
 *
 * This module provides comprehensive type definitions for all client-side browser scripts
 * that are injected into browser contexts. These interfaces mirror the methods exposed
 * on window objects by injected client scripts, enabling type-safe interaction through
 * the CDP abstraction layer.
 *
 * @example
 * ```typescript
 * import { withCdp } from '../client-script-abstraction';
 * import type { <PERSON><PERSON>, BrowserController } from './client-scripts';
 *
 * const clientScripts = withCdp(cdp, executionContextId, sessionId);
 * await clientScripts.BrowserController.init();
 * ```
 */

/**
 * Viewport dimensions for browser operations
 */
export interface Viewport {
  /** Width in pixels */
  width: number;
  /** Height in pixels */
  height: number;
}

/**
 * Bounding box coordinates for UI elements or screen regions
 */
export interface BoundingBox {
  /** X coordinate (left edge) */
  x: number;
  /** Y coordinate (top edge) */
  y: number;
  /** Width in pixels */
  width: number;
  /** Height in pixels */
  height: number;
}

/**
 * Result from screenshot capture operations
 */
export interface ScreenshotResult {
  /** Base64-encoded screenshot data */
  data?: string;
  /** Whether the operation was successful */
  success?: boolean;
  /** Error message if operation failed */
  error?: string;
  /** Whether this is a fallback result */
  fallback?: boolean;
  /** Time taken to process the screenshot in milliseconds */
  processingTime?: number;
  /** Whether PII redaction was applied */
  redactionApplied?: boolean;
  /** Detailed statistics about PII redaction */
  redactionStats?: {
    /** Number of items that were redacted */
    redactedCount?: number;
    /** Time taken for redaction processing in milliseconds */
    processingTime?: number;
    /** Mappings of redacted elements */
    mappings?: Array<{
      /** Type of HTML element that was redacted */
      elementType: string;
      [key: string]: unknown;
    }>;
    /** Statistics about restoration operations */
    restorationStats?: {
      /** Number of items successfully restored */
      restoredCount?: number;
      /** Number of items that failed to restore */
      failedCount?: number;
      /** Array of error messages from restoration failures */
      errors?: string[];
    };
  };
}

/**
 * Page metadata and information
 */
export interface PageInfo {
  /** Current page URL */
  url: string;
  /** Page title */
  title: string;
  /** Additional page properties */
  [key: string]: unknown;
}

/**
 * Input event data for user interactions
 */
export interface InputEventData {
  /** Type of input event (e.g., 'click', 'keydown', 'text-insert') */
  type: string;
  /** X coordinate for mouse events */
  x?: number;
  /** Y coordinate for mouse events */
  y?: number;
  /** Key code or key name for keyboard events */
  key?: string;
  /** Text content for text insertion events */
  text?: string;
  /** Additional event properties */
  [key: string]: unknown;
}

/**
 * Status of PII (Personally Identifiable Information) redaction
 */
export interface PIIRedactionStatus {
  /** Whether PII redaction is currently active */
  isRedacted: boolean;
  /** Additional status properties */
  [key: string]: unknown;
}

/**
 * Browser Controller interface - handles CDP operations and user interactions
 * Exposed on window.browserController
 */
export interface BrowserController {
  /**
   * Initialize the browser controller proxy
   */
  init(): Promise<void>;

  /**
   * Setup browser metrics and viewport configuration
   */
  setupBrowserMetrics(viewport: Viewport): Promise<void>;

  /**
   * Dispatch mouse movement event
   */
  dispatchMouseMove(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse down event
   */
  dispatchMouseDown(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse up event
   */
  dispatchMouseUp(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse click event
   */
  dispatchMouseClick(x: number, y: number): Promise<void>;

  /**
   * Dispatch keyboard event
   */
  dispatchKeyEvent(eventData: InputEventData): Promise<void>;

  /**
   * Insert text at current cursor position
   */
  insertText(text: string): Promise<void>;

  /**
   * Take a screenshot of the current page
   */
  takeScreenshot(): Promise<ScreenshotResult>;

  /**
   * Capture screenshot with PII redaction applied
   */
  captureScreenshotWithRedaction(): Promise<ScreenshotResult>;

  /**
   * Handle comprehensive input event processing
   */
  handleInputEvent(eventData: InputEventData): Promise<void>;

  /**
   * Request a new frame generation
   */
  requestNewFrame(): Promise<void>;

  /**
   * Trigger mouse movement to generate UI updates
   */
  triggerMouseMovement(): Promise<void>;

  /**
   * Get page metadata and information
   */
  getPageInfo(): Promise<PageInfo>;

  /**
   * Test connectivity with control tab
   */
  ping(): Promise<string>;
}

/**
 * Screen Cropper interface - handles WebRTC streaming and screen capture
 * Exposed on window.screenCropper
 */
export interface ScreenCropper {
  /**
   * Initialize screen cropper with WebSocket and WebRTC connections
   * Sets up connections but doesn't start streaming
   */
  init(wsEndpoint: string, viewport: Viewport): Promise<string>;

  /**
   * Start screen cropper streaming with captcha detection
   */
  start(viewport: Viewport): Promise<void>;

  /**
   * Stop streaming and clean up resources
   */
  stopStreaming(): Promise<void>;

  /**
   * Update the crop box for focused capture
   */
  updateCropBox(cropBox: BoundingBox): Promise<void>;

  /**
   * Start capturing frames for captcha detector
   */
  startCapturingForCaptchaDetector(): Promise<void>;

  /**
   * Stop capturing frames for captcha detector
   */
  stopCapturingForCaptchaDetector(): Promise<void>;

  /**
   * Pause frame sending temporarily
   */
  pauseFrameSending(): void;

  /**
   * Resume frame sending
   */
  resumeFrameSending(): void;
}

export interface CaptchaDetectorConfig {
  diffThreshold?: number;
  screenshotQuality?: number;
  debug?: boolean;
  comparisonIntervalMs?: number;
  sampling?: number;
  settleThreshold?: number;
  consecutiveSettledFrames?: number;
  maxWaitDuration?: number;
}

export interface ComparisonResult {
  percentageDiff: number;
  numDiffPixels: number;
  comparisonTime: number;
}

/**
 * Captcha Detector interface - handles screenshot comparison and UI change detection
 * Exposed on window.captchaDetector
 */
export interface CaptchaDetector {
  /**
   * Initialize the captcha detector with configuration
   */
  initialize(options?: CaptchaDetectorConfig): void;

  /**
   * Clean up resources and event listeners
   */
  cleanup(): void;

  /**
   * Trigger screenshot comparison manually
   */
  triggerScreenshotComparison(): Promise<ComparisonResult>;

  /**
   * Get current configuration
   */
  getConfig(): Promise<CaptchaDetectorConfig>;

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<CaptchaDetectorConfig>): void;
}

export interface TensorFlowDetectorConfig {
  scoreThreshold?: number;
  debug?: boolean;
  inputSize?: number;
  maxDetections?: number;
}

export interface DetectionResult {
  class: string;
  score: number;
  bbox: BoundingBox;
}

/**
 * TensorFlow Captcha Detector interface - handles ML-based captcha detection
 * Exposed on window.tfCaptchaDetector
 */
export interface TensorFlowCaptchaDetector {
  /**
   * Initialize the TensorFlow detector with configuration
   */
  initialize(options?: TensorFlowDetectorConfig): Promise<boolean>;

  /**
   * Find the best captcha bounding box from detections
   */
  findBestCaptchaBoundingBox(
    detections: DetectionResult[],
    viewport: Viewport,
  ): Promise<BoundingBox>;

  /**
   * Take screenshot and detect objects in one operation
   */
  takeScreenshotAndDetect(dimensions: Viewport): Promise<DetectionResult[]>;

  /**
   * Get initial bounding box for captcha area
   */
  getInitialBoundingBox(viewport: Viewport): Promise<BoundingBox>;

  /**
   * Refresh and update bounding box
   */
  refreshBoundingBox(viewport: Viewport): Promise<BoundingBox>;
}

export interface ScreenshotComparisonOptions {
  threshold?: number;
  includeAA?: boolean;
  diffMask?: boolean;
  sampling?: number;
}

export interface ImageData {
  data: Uint8ClampedArray;
  width: number;
  height: number;
}

/**
 * Screenshot Comparison Utilities interface - handles image comparison operations
 * Exposed on window.screenshotComparisonUtils
 */
export interface ScreenshotComparisonUtils {
  /**
   * Compare two screenshots and return difference metrics
   */
  compareScreenshots(
    img1: ImageData,
    img2: ImageData,
    options?: ScreenshotComparisonOptions,
  ): ComparisonResult;

  /**
   * Prepare buffer for comparison operations
   */
  prepareBufferForComparison(width: number, height: number): Uint8ClampedArray;

  /**
   * Create test image for testing purposes
   */
  createTestImage(
    width: number,
    height: number,
    color?: [number, number, number, number],
  ): ImageData;

  /**
   * Create image with differences for testing
   */
  createImageWithDifferences(
    baseImage: ImageData,
    differences: Array<{ x: number; y: number; color: [number, number, number, number] }>,
  ): ImageData;
}

/**
 * Combined interface representing all client scripts available on window object
 */
export interface ClientApi {
  browserController: BrowserController;
  screenCropper: ScreenCropper;
  captchaDetector: CaptchaDetector;
  tfCaptchaDetector: TensorFlowCaptchaDetector;
}
