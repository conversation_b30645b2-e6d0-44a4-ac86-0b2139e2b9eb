/**
 * Client Script Abstraction Layer
 *
 * This module provides a typed interface for calling client-side browser scripts through
 * CDP Runtime.evaluate. It replaces string-based CDP calls with a discoverable, type-safe API.
 *
 * ## Benefits
 * - **Type Safety**: Full TypeScript support with IntelliSense
 * - **Discoverability**: Easy to find available methods and their signatures
 * - **Error Handling**: Consistent error handling across all client script calls
 * - **Maintainability**: Centralized abstraction for all client script interactions
 *
 * ## Architecture
 * The abstraction uses proxy classes that implement the same interfaces as the client scripts,
 * but internally convert method calls to CDP Runtime.evaluate expressions.
 *
 * ## Usage Examples
 *
 * ### Basic Browser Controller Operations
 * ```typescript
 * const clientScripts = withCdp(cdp, executionContextId, sessionId);
 *
 * // Initialize browser controller
 * await clientScripts.BrowserController.init();
 *
 * // Take a screenshot
 * const screenshot = await clientScripts.BrowserController.takeScreenshot();
 *
 * // Perform user interactions
 * await clientScripts.BrowserController.dispatchMouseClick(100, 200);
 * await clientScripts.BrowserController.insertText("Hello World");
 * ```
 *
 * ### Screen Cropper Operations
 * ```typescript
 * // Initialize screen cropper
 * await clientScripts.ScreenCropper.init(wsEndpoint, viewport);
 *
 * // Start streaming
 * await clientScripts.ScreenCropper.start(viewport);
 *
 * // Update crop region
 * await clientScripts.ScreenCropper.updateCropBox({ x: 0, y: 0, width: 640, height: 480 });
 * ```
 *
 * ### TensorFlow Detector Operations
 * ```typescript
 * // Initialize with configuration
 * await clientScripts.TensorFlowCaptchaDetector.initialize({
 *   scoreThreshold: 0.5,
 *   debug: true
 * });
 *
 * // Get captcha bounding box
 * const boundingBox = await clientScripts.TensorFlowCaptchaDetector.getInitialBoundingBox(viewport);
 * ```
 *
 * ## Limitations
 * Some methods that involve complex data types (like Uint8ClampedArray or ImageData) cannot
 * be called through CDP and will throw descriptive errors directing you to use client-side calls.
 */

import type { CDPInstance } from './simple-cdp';
import type {
  BrowserController,
  ScreenCropper,
  CaptchaDetector,
  TensorFlowCaptchaDetector,
  Viewport,
  BoundingBox,
  ScreenshotResult,
  PageInfo,
  InputEventData,
  PIIRedactionStatus,
  CaptchaDetectorConfig,
  ComparisonResult,
  TensorFlowDetectorConfig,
  DetectionResult,
} from './types/client-api-types';

/**
 * Configuration for CDP method calls
 */
interface CDPCallConfig {
  /** CDP client instance */
  cdp: CDPInstance;
  /** Execution context ID for the target tab */
  executionContextId: number;
  /** Session ID for the target tab */
  sessionId: string;
}

/**
 * Helper function to execute client script methods through CDP Runtime.evaluate
 *
 * This function handles the low-level CDP communication, error handling, and result extraction.
 * It converts JavaScript expressions into CDP Runtime.evaluate calls and processes the results.
 *
 * @template T - Expected return type of the client script method
 * @param config - CDP configuration containing client, context, and session info
 * @param scriptExpression - JavaScript expression to execute in the browser context
 * @param awaitPromise - Whether to wait for promise resolution (default: true)
 * @returns Promise resolving to the method result
 * @throws Error if the client script execution fails
 */
async function executeClientMethod<T = unknown>(
  config: CDPCallConfig,
  scriptExpression: string,
  awaitPromise: boolean = true,
): Promise<T> {
  const result = await config.cdp.Runtime.evaluate(
    {
      expression: scriptExpression,
      contextId: config.executionContextId,
      awaitPromise,
      returnByValue: true,
    },
    config.sessionId,
  );

  if (result.exceptionDetails) {
    throw new Error(
      `Client script execution failed: ${result.exceptionDetails.text || 'Unknown error'}`,
    );
  }

  return result.result?.value as T;
}

/**
 * Browser Controller abstraction - proxies calls to window.browserController
 */
class BrowserControllerProxy implements BrowserController {
  constructor(private config: CDPCallConfig) {}

  async init(): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { await window.browserController.init(); })()`,
    );
  }

  async setupBrowserMetrics(viewport: Viewport): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.setupBrowserMetrics(${JSON.stringify(viewport)}); 
      })()`,
    );
  }

  async dispatchMouseMove(x: number, y: number): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.dispatchMouseMove(${x}, ${y}); 
      })()`,
    );
  }

  async dispatchMouseDown(x: number, y: number): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.dispatchMouseDown(${x}, ${y}); 
      })()`,
    );
  }

  async dispatchMouseUp(x: number, y: number): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.dispatchMouseUp(${x}, ${y}); 
      })()`,
    );
  }

  async dispatchMouseClick(x: number, y: number): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.dispatchMouseClick(${x}, ${y}); 
      })()`,
    );
  }

  async dispatchKeyEvent(eventData: InputEventData): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.dispatchKeyEvent(${JSON.stringify(eventData)}); 
      })()`,
    );
  }

  async insertText(text: string): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.insertText(${JSON.stringify(text)}); 
      })()`,
    );
  }

  async takeScreenshot(): Promise<ScreenshotResult> {
    return executeClientMethod<ScreenshotResult>(
      this.config,
      `(async () => { 
        return await window.browserController.takeScreenshot(); 
      })()`,
    );
  }

  async captureScreenshotWithRedaction(): Promise<ScreenshotResult> {
    return executeClientMethod<ScreenshotResult>(
      this.config,
      `(async () => { 
        return await window.browserController.captureScreenshotWithRedaction(); 
      })()`,
    );
  }

  async handleInputEvent(eventData: InputEventData): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.handleInputEvent(${JSON.stringify(eventData)}); 
      })()`,
    );
  }

  async requestNewFrame(): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.requestNewFrame(); 
      })()`,
    );
  }

  async triggerMouseMovement(): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.triggerMouseMovement(); 
      })()`,
    );
  }

  async getPageInfo(): Promise<PageInfo> {
    return executeClientMethod<PageInfo>(
      this.config,
      `(async () => { 
        return await window.browserController.getPageInfo(); 
      })()`,
    );
  }

  async ping(): Promise<string> {
    return executeClientMethod<string>(
      this.config,
      `(async () => { 
        return await window.browserController.ping(); 
      })()`,
    );
  }

  async redactPII(): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.redactPII(); 
      })()`,
    );
  }

  async restorePII(): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => { 
        await window.browserController.restorePII(); 
      })()`,
    );
  }

  async getPIIRedactionStatus(): Promise<PIIRedactionStatus> {
    return executeClientMethod<PIIRedactionStatus>(
      this.config,
      `(async () => {
        return await window.browserController.getPIIRedactionStatus();
      })()`,
    );
  }
}

/**
 * Screen Cropper abstraction - proxies calls to window.screenCropper
 */
class ScreenCropperProxy implements ScreenCropper {
  constructor(private config: CDPCallConfig) {}

  async init(wsEndpoint: string, viewport: Viewport): Promise<string> {
    return executeClientMethod<string>(
      this.config,
      `(async () => {
        return await window.screenCropper.init(${JSON.stringify(wsEndpoint)}, ${JSON.stringify(viewport)});
      })()`,
    );
  }

  async start(viewport: Viewport): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => {
        await window.screenCropper.start(${JSON.stringify(viewport)});
      })()`,
    );
  }

  async stopStreaming(): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => {
        await window.screenCropper.stopStreaming();
      })()`,
    );
  }

  async updateCropBox(cropBox: BoundingBox): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => {
        await window.screenCropper.updateCropBox(${JSON.stringify(cropBox)});
      })()`,
    );
  }

  async startCapturingForCaptchaDetector(): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => {
        await window.screenCropper.startCapturingForCaptchaDetector();
      })()`,
    );
  }

  async stopCapturingForCaptchaDetector(): Promise<void> {
    await executeClientMethod(
      this.config,
      `(async () => {
        await window.screenCropper.stopCapturingForCaptchaDetector();
      })()`,
    );
  }

  pauseFrameSending(): void {
    executeClientMethod(this.config, `window.screenCropper.pauseFrameSending()`, false);
  }

  resumeFrameSending(): void {
    executeClientMethod(this.config, `window.screenCropper.resumeFrameSending()`, false);
  }
}

/**
 * Captcha Detector abstraction - proxies calls to window.captchaDetector
 */
class CaptchaDetectorProxy implements CaptchaDetector {
  constructor(private config: CDPCallConfig) {}

  initialize(options?: CaptchaDetectorConfig): void {
    executeClientMethod(
      this.config,
      `window.captchaDetector.initialize(${JSON.stringify(options || {})})`,
      false,
    );
  }

  cleanup(): void {
    executeClientMethod(this.config, `window.captchaDetector.cleanup()`, false);
  }

  async triggerScreenshotComparison(): Promise<ComparisonResult> {
    return executeClientMethod<ComparisonResult>(
      this.config,
      `(async () => {
        return await window.captchaDetector.triggerScreenshotComparison();
      })()`,
    );
  }

  updateDebugPanel(data: any): void {
    executeClientMethod(
      this.config,
      `window.captchaDetector.updateDebugPanel(${JSON.stringify(data)})`,
      false,
    );
  }

  async getStoredImages(): Promise<unknown[]> {
    return executeClientMethod<unknown[]>(
      this.config,
      `window.captchaDetector.getStoredImages()`,
      false,
    );
  }

  async getComparisonHistory(): Promise<unknown[]> {
    return executeClientMethod<unknown[]>(
      this.config,
      `window.captchaDetector.getComparisonHistory()`,
      false,
    );
  }

  clearDebugData(): void {
    executeClientMethod(this.config, `window.captchaDetector.clearDebugData()`, false);
  }

  async getConfig(): Promise<CaptchaDetectorConfig> {
    return executeClientMethod<CaptchaDetectorConfig>(
      this.config,
      `window.captchaDetector.getConfig()`,
      false,
    );
  }

  updateConfig(newConfig: Partial<CaptchaDetectorConfig>): void {
    executeClientMethod(
      this.config,
      `window.captchaDetector.updateConfig(${JSON.stringify(newConfig)})`,
      false,
    );
  }

  setDebugToggleVisibility(visible: boolean): void {
    executeClientMethod(
      this.config,
      `window.captchaDetector.setDebugToggleVisibility(${visible})`,
      false,
    );
  }

  setAutoShowDebugPanel(autoShow: boolean): void {
    executeClientMethod(
      this.config,
      `window.captchaDetector.setAutoShowDebugPanel(${autoShow})`,
      false,
    );
  }
}

/**
 * TensorFlow Captcha Detector abstraction - proxies calls to window.tfCaptchaDetector
 */
class TensorFlowCaptchaDetectorProxy implements TensorFlowCaptchaDetector {
  constructor(private config: CDPCallConfig) {}

  async initialize(options?: TensorFlowDetectorConfig): Promise<boolean> {
    return executeClientMethod<boolean>(
      this.config,
      `(async () => {
        return await window.tfCaptchaDetector.initialize(${JSON.stringify(options || {})});
      })()`,
    );
  }

  async findBestCaptchaBoundingBox(
    detections: DetectionResult[],
    viewport: Viewport,
  ): Promise<BoundingBox> {
    return executeClientMethod<BoundingBox>(
      this.config,
      `window.tfCaptchaDetector.findBestCaptchaBoundingBox(${JSON.stringify(detections)}, ${JSON.stringify(viewport)})`,
      false,
    );
  }

  async takeScreenshotAndDetect(dimensions: Viewport): Promise<DetectionResult[]> {
    return executeClientMethod<DetectionResult[]>(
      this.config,
      `(async () => {
        return await window.tfCaptchaDetector.takeScreenshotAndDetect(${JSON.stringify(dimensions)});
      })()`,
    );
  }

  async getInitialBoundingBox(viewport: Viewport): Promise<BoundingBox> {
    return executeClientMethod<BoundingBox>(
      this.config,
      `(async () => {
        return await window.tfCaptchaDetector.getInitialBoundingBox(${JSON.stringify(viewport)});
      })()`,
    );
  }

  async refreshBoundingBox(viewport: Viewport): Promise<BoundingBox> {
    return executeClientMethod<BoundingBox>(
      this.config,
      `(async () => {
        return await window.tfCaptchaDetector.refreshBoundingBox(${JSON.stringify(viewport)});
      })()`,
    );
  }
}

/**
 * Main client scripts abstraction interface
 */
export interface ClientScriptsAbstraction {
  BrowserController: BrowserController;
  ScreenCropper: ScreenCropper;
  CaptchaDetector: CaptchaDetector;
  TensorFlowCaptchaDetector: TensorFlowCaptchaDetector;
}

/**
 * Create a typed abstraction layer for client scripts
 *
 * @param cdp - CDP client instance
 * @param executionContextId - Execution context ID for the target tab
 * @param sessionId - Session ID for the target tab
 * @returns Typed interface for calling client script methods
 *
 * @example
 * ```typescript
 * const clientScripts = withCdp(cdpClient, executionContextId, sessionId);
 *
 * // Initialize browser controller
 * await clientScripts.BrowserController.init();
 *
 * // Take a screenshot
 * const screenshot = await clientScripts.BrowserController.takeScreenshot();
 *
 * // Start screen cropper
 * await clientScripts.ScreenCropper.start(viewport);
 *
 * // Initialize TensorFlow detector
 * await clientScripts.TensorFlowCaptchaDetector.initialize({ scoreThreshold: 0.5 });
 * ```
 */
export function withCdp(
  cdp: CDPInstance,
  executionContextId: number,
  sessionId: string,
): ClientScriptsAbstraction {
  const config: CDPCallConfig = { cdp, executionContextId, sessionId };

  return {
    BrowserController: new BrowserControllerProxy(config),
    ScreenCropper: new ScreenCropperProxy(config),
    CaptchaDetector: new CaptchaDetectorProxy(config),
    TensorFlowCaptchaDetector: new TensorFlowCaptchaDetectorProxy(config),
  };
}
